#!/usr/bin/env python3
"""
Script to fetch an existing Braintrust dataset and analyze its timestamps.

This script demonstrates how to:
- Connect to an existing dataset
- Read and analyze created timestamps from existing records
- Display timestamp information for all records
- Show transaction IDs to understand update history
"""

import braintrust
from datetime import datetime
from typing import Dict, Any, List


def analyze_existing_dataset():
    """Fetch and analyze timestamps from an existing dataset."""
    
    # Connect to the existing dataset
    print("=== Connecting to Existing Dataset ===")
    dataset = braintrust.init_dataset(project="pedro-project1", name="image dataset")
    
    print(f"Connected to dataset: {dataset.name}")
    print(f"Project: {dataset.project.name}")
    print()
    
    print("=== Reading All Records with Timestamps ===")
    
    # Collect all records for analysis
    all_records = []
    created_timestamps = []
    transaction_ids = set()
    
    record_count = 0
    for record in dataset:
        record_count += 1
        all_records.append(record)
        
        record_id = record['id']
        created_timestamp = record['created']
        transaction_id = record['_xact_id']
        
        # Parse the ISO timestamp
        if created_timestamp.endswith('Z'):
            created_timestamp_clean = created_timestamp[:-1] + '+00:00'
        else:
            created_timestamp_clean = created_timestamp
            
        try:
            created_dt = datetime.fromisoformat(created_timestamp_clean)
            created_timestamps.append(created_dt)
        except ValueError:
            print(f"Warning: Could not parse timestamp for record {record_id}: {created_timestamp}")
            continue
        
        # Collect transaction IDs
        transaction_ids.add(transaction_id)
        
        print(f"Record {record_count}: {record_id}")
        print(f"  Created: {created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"  Transaction ID: {transaction_id}")
        print(f"  Input: {str(record.get('input', 'N/A'))[:100]}{'...' if len(str(record.get('input', ''))) > 100 else ''}")
        print(f"  Expected: {str(record.get('expected', 'N/A'))[:100]}{'...' if len(str(record.get('expected', ''))) > 100 else ''}")
        
        # Show metadata if present
        metadata = record.get('metadata', {})
        if metadata:
            print(f"  Metadata keys: {list(metadata.keys())}")
        
        print()
    
    if record_count == 0:
        print("No records found in the dataset.")
        return
    
    print("=== Dataset Analysis ===")
    
    # Basic statistics
    print(f"Total records: {record_count}")
    print(f"Unique transaction IDs: {len(transaction_ids)}")
    
    if created_timestamps:
        earliest = min(created_timestamps)
        latest = max(created_timestamps)
        time_span = latest - earliest
        
        print(f"Earliest record: {earliest.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"Latest record: {latest.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"Time span: {time_span.total_seconds() / 3600:.2f} hours")
        
        # Group records by date
        records_by_date = {}
        for i, record in enumerate(all_records):
            if i < len(created_timestamps):
                date_key = created_timestamps[i].date()
                if date_key not in records_by_date:
                    records_by_date[date_key] = 0
                records_by_date[date_key] += 1
        
        print(f"\nRecords by creation date:")
        for date_key in sorted(records_by_date.keys()):
            count = records_by_date[date_key]
            print(f"  {date_key}: {count} record{'s' if count != 1 else ''}")
    
    print()
    
    # Check for potential updates (records with higher transaction IDs)
    if len(transaction_ids) > 1:
        sorted_xact_ids = sorted(transaction_ids)
        print("=== Update Analysis ===")
        print(f"Transaction ID range: {sorted_xact_ids[0]} to {sorted_xact_ids[-1]}")
        
        # Find records that might have been updated
        xact_id_counts = {}
        for record in all_records:
            xact_id = record['_xact_id']
            if xact_id not in xact_id_counts:
                xact_id_counts[xact_id] = []
            xact_id_counts[xact_id].append(record['id'])
        
        print(f"Records per transaction ID:")
        for xact_id in sorted(xact_id_counts.keys()):
            record_ids = xact_id_counts[xact_id]
            print(f"  Transaction {xact_id}: {len(record_ids)} record{'s' if len(record_ids) != 1 else ''}")
        
        # If there are gaps in transaction IDs, some records might have been updated
        if len(sorted_xact_ids) < (sorted_xact_ids[-1] - sorted_xact_ids[0] + 1):
            print("Note: Gaps in transaction IDs suggest some records may have been updated.")
    
    print()
    
    # Look for custom timestamp tracking in metadata
    print("=== Custom Timestamp Analysis ===")
    records_with_custom_timestamps = 0
    custom_timestamp_fields = set()
    
    for record in all_records:
        metadata = record.get('metadata', {})
        for key in metadata.keys():
            if any(time_word in key.lower() for time_word in ['time', 'date', 'created', 'updated', 'timestamp']):
                custom_timestamp_fields.add(key)
                records_with_custom_timestamps += 1
                break
    
    if custom_timestamp_fields:
        print(f"Found {records_with_custom_timestamps} records with custom timestamp fields:")
        for field in sorted(custom_timestamp_fields):
            print(f"  - {field}")
        
        # Show examples of custom timestamp fields
        print("\nExamples of custom timestamp values:")
        shown_examples = set()
        for record in all_records[:5]:  # Show first 5 records
            metadata = record.get('metadata', {})
            for field in custom_timestamp_fields:
                if field in metadata and field not in shown_examples:
                    print(f"  {field}: {metadata[field]}")
                    shown_examples.add(field)
    else:
        print("No custom timestamp fields found in metadata.")
    
    print()
    print("=== Summary ===")
    print("Dataset timestamp information:")
    print(f"• Found {record_count} records in the dataset")
    print("• Each record has a 'created' field with the original creation timestamp")
    print("• Transaction IDs (_xact_id) can be used to detect updates")
    print("• Created timestamps show when records were first added to the dataset")
    if custom_timestamp_fields:
        print("• Custom timestamp fields found in metadata for additional tracking")


def show_recent_records(hours: int = 24):
    """Show records created in the last N hours."""
    print(f"\n=== Records Created in Last {hours} Hours ===")
    
    dataset = braintrust.init_dataset(project="pedro-project1", name="image dataset")
    
    cutoff_time = datetime.now().timestamp() - (hours * 3600)
    recent_records = []
    
    for record in dataset:
        created_timestamp = record['created']
        if created_timestamp.endswith('Z'):
            created_timestamp = created_timestamp[:-1] + '+00:00'
        
        try:
            created_dt = datetime.fromisoformat(created_timestamp)
            if created_dt.timestamp() > cutoff_time:
                recent_records.append((record, created_dt))
        except ValueError:
            continue
    
    if recent_records:
        print(f"Found {len(recent_records)} recent records:")
        for record, created_dt in sorted(recent_records, key=lambda x: x[1], reverse=True):
            print(f"  {record['id']}: {created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    else:
        print(f"No records found created in the last {hours} hours.")


def main():
    """Main function to analyze the existing dataset."""
    try:
        analyze_existing_dataset()
        show_recent_records(24)  # Show records from last 24 hours
        
    except Exception as e:
        print(f"Error accessing dataset: {e}")
        print("\nPossible issues:")
        print("• Dataset 'image dataset' might not exist in project 'pedro-project1'")
        print("• Check your Braintrust credentials and permissions")
        print("• Verify the project and dataset names are correct")


if __name__ == "__main__":
    main()
